import InteractiveTechStack from "@/components/sections/technology/InteractiveTechStack";
import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Link from "next/link";

export const metadata = {
  title: "Technology Showcase | Motshwanelo IT Consulting",
  description: "Explore our technology stack in an interactive visualization.",
};

export default function TechnologyShowcasePage() {
  return (
    <Layout>
      <SectionHeader
        title="Technology Showcase"
        group_page="Cutting-Edge Solutions"
        current_page="Technology Showcase"
        display=""
      />

      <InteractiveTechStack />

      <section className="technology-cta-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <span className="span">Innovation Driven</span>
                <h2>Why Our Technology Matters</h2>
                <div className="space16" />
                <p>
                  At Motshwanelo IT Consulting, we carefully select the best technologies for each project,
                  ensuring that our solutions are not only effective today but also adaptable for tomorrow's challenges.
                  Our expertise across multiple technology domains allows us to create integrated systems that deliver
                  real business value.
                </p>
                <div className="space30" />
                <Link className="theme-btn1" href="/contact">
                  Discuss Your Technology Needs
                  <span>
                    <i className="fa-solid fa-arrow-right" />
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}