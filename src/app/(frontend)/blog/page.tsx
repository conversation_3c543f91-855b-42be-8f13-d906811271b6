import Layout from "@/components/layout/Layout";
import SectionHeader from "@/components/layout/SectionHeader";
import Section1 from "@/components/sections/blog/Section1";
import Section2 from "@/components/sections/about/Section3";
import { BlogListing } from "@/components/blog/blog-listing";
import { BlogService } from "@/lib/services/blog.service";
import type { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Industry Insights | Motshwanelo IT Consulting - Latest Technology Trends & News",
  description: "Stay updated with the latest IT trends, digital transformation insights, and technology news from Motshwanelo IT Consulting. Expert analysis on Smart Cities, Data Centres, and enterprise solutions.",
  keywords: ["IT Blog", "Technology News", "Digital Transformation", "Smart City Trends", "Data Centre News", "IT Industry Insights"],
  openGraph: {
    title: "Industry Insights | Latest Technology Trends & News",
    description: "Stay updated with expert insights on digital transformation, Smart Cities, Data Centres, and the latest IT industry trends.",
    type: "website",
    url: "https://motshwaneloitconsulting.co.za/blog",
  },
};

async function BlogContent() {
  try {
    const [postsResult, categories, tags] = await Promise.all([
      BlogService.getPosts({
        page: 1,
        perPage: 12,
        status: 'PUBLISHED'
      }),
      BlogService.getCategories(),
      BlogService.getTags()
    ])

    return (
      <div className="blog-content-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <BlogListing
                initialData={postsResult}
                categories={categories}
                tags={tags}
                itemsPerPage={12}
              />
            </div>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error loading blog data:', error)
    return (
      <div className="blog-error-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto text-center">
              <div className="heading1">
                <h2>Unable to load blog posts</h2>
                <div className="space16" />
                <p>We're having trouble loading the blog posts. Please try again later.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
}

function BlogSkeleton() {
  return (
    <div className="blog-skeleton-section sp">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <div className="skeleton-title"></div>
            </div>
          </div>
        </div>
        <div className="space60" />
        <div className="row">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="col-lg-4 col-md-6">
              <div className="blog-skeleton-card">
                <div className="skeleton-image"></div>
                <div className="skeleton-content">
                  <div className="skeleton-line skeleton-line-title"></div>
                  <div className="skeleton-line skeleton-line-subtitle"></div>
                  <div className="skeleton-line skeleton-line-text"></div>
                  <div className="skeleton-line skeleton-line-text-short"></div>
                </div>
              </div>
              <div className="space30" />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function Blog() {
    return (
        <>
            <Layout>
                <SectionHeader title="Industry Insights & News" group_page="Latest Technology Trends from Our Experts" current_page="Blog" display="d-none" />

                {/* Enhanced Blog Listing */}
                <Suspense fallback={<BlogSkeleton />}>
                  <BlogContent />
                </Suspense>

                {/* Keep existing sections for backward compatibility */}
                <Section1 />
                <Section2 />
            </Layout>
        </>
    );
}
