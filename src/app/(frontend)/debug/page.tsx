"use client"

import { useSession } from "next-auth/react"
import Layout from "@/components/layout/Layout"
import SectionHeader from "@/components/layout/SectionHeader"
import Link from "next/link"

export default function DebugPage() {
  const { data: session, status } = useSession()

  return (
    <Layout>
      <SectionHeader
        title="Authentication Debug"
        group_page="Development Tools"
        current_page="Debug"
        display=""
      />

      <div className="debug-section sp">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 m-auto">
              <div className="debug-content">
                <div className="debug-card">
                  <h3>Session Status</h3>
                  <div className="space16" />
                  <p><strong>Status:</strong> {status}</p>
                </div>
                <div className="space30" />

                <div className="debug-card">
                  <h3>Session Data</h3>
                  <div className="space16" />
                  <div className="debug-code">
                    <pre>{JSON.stringify(session, null, 2)}</pre>
                  </div>
                </div>
                <div className="space30" />

                <div className="debug-card">
                  <h3>User Role Check</h3>
                  <div className="space16" />
                  <p><strong>User Role:</strong> {session?.user?.role || "No role found"}</p>
                  <p><strong>Is Admin:</strong> {session?.user?.role === "ADMIN" ? "Yes" : "No"}</p>
                </div>
                <div className="space30" />

                <div className="debug-actions">
                  <Link className="theme-btn1" href="/auth/signin">
                    Sign In
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                  <Link className="theme-btn2" href="/admin">
                    Try Admin Access
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                  <Link className="theme-btn3" href="/">
                    Home
                    <span>
                      <i className="fa-solid fa-arrow-right" />
                    </span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}