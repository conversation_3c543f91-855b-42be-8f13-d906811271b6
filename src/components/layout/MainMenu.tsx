import Link from "next/link";
import { usePathname } from "next/navigation";

export default function MainMenu() {
    const pathname = usePathname();

    const isActive = (path: string) => {
        if (path === "/" && pathname === "/") return true;
        if (path !== "/" && pathname.startsWith(path)) return true;
        return false;
    };

    return (
        <ul className="main-nav">
            <li>
                <Link
                    href="/"
                    className={`nav-link ${isActive("/") ? "active" : ""}`}
                >
                    Home
                </Link>
            </li>
            <li>
                <Link
                    href="/about"
                    className={`nav-link ${isActive("/about") ? "active" : ""}`}
                >
                    About
                </Link>
            </li>
            <li className="dropdown-menu-parrent">
                <Link href="/service" className={`main1 nav-link ${isActive("/service") ? "active" : ""}`}>
                    Services <i className="fa-solid fa-angle-down ml-1" />
                </Link>
                <ul className="dropdown-menu">
                    <li>
                        <Link href="/service/data-centre" className="dropdown-item">
                            Data Centre Solutions
                        </Link>
                    </li>
                    <li>
                        <Link href="/service/smart-city" className="dropdown-item">
                            Smart City Solutions
                        </Link>
                    </li>
                    <li>
                        <Link href="/service/it-consulting" className="dropdown-item">
                            IT Consulting
                        </Link>
                    </li>
                    <li>
                        <Link href="/service/software-development" className="dropdown-item">
                            Software Development
                        </Link>
                    </li>
                </ul>
            </li>
            <li className="dropdown-menu-parrent">
                <Link href="/projects" className={`main1 nav-link ${isActive("/projects") ? "active" : ""}`}>
                    Projects <i className="fa-solid fa-angle-down ml-1" />
                </Link>
                <ul className="dropdown-menu">
                    <li>
                        <Link href="/projects" className="dropdown-item">
                            All Projects
                        </Link>
                    </li>
                    <li>
                        <Link href="/success-stories" className="dropdown-item">
                            Success Stories
                        </Link>
                    </li>
                </ul>
            </li>
            <li>
                <Link
                    href="/blog"
                    className={`nav-link ${isActive("/blog") ? "active" : ""}`}
                >
                    Blog
                </Link>
            </li>
            <li>
                <Link
                    href="/contact"
                    className={`nav-link ${isActive("/contact") ? "active" : ""}`}
                >
                    Contact
                </Link>
            </li>
        </ul>
    );
}
