"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";

interface MobileMenuProps {
    isMobileMenu: boolean;
    handleMobileMenu: () => void;
}

export default function MobileMenu({ isMobileMenu, handleMobileMenu }: MobileMenuProps) {
    const [openSubMenus, setOpenSubMenus] = useState<{ [key: string]: boolean }>({});
    const pathname = usePathname();

    useEffect(() => {
        if (isMobileMenu) {
            handleMobileMenu();
        }
    }, [pathname]);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth > 991) {
                setOpenSubMenus({});
            }
        };
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    const handleToggleSubMenu = (key: string) => {
        setOpenSubMenus((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const isHashNav = (href: string) => href === "#";

    return (
        <>
            {isMobileMenu && <div className="mobile-menu-overlay" onClick={handleMobileMenu} />}
            {/*=====Mobile header start=======*/}
            <div className={`mobile-sidebar d-block d-lg-none ${isMobileMenu ? "mobile-menu-active" : ""}`}>
                <div className="logo-m">
                    <Link href="/" className="flex items-center justify-center">
                        <img
                            src="assets/img/logo/logo_full.svg"
                            alt="Motshwanelo IT Consulting"
                            className="h-12 w-auto filter brightness-0 invert"
                        />
                    </Link>
                </div>
                <button
                    className="menu-close flex items-center justify-center w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300 absolute top-6 right-6"
                    onClick={handleMobileMenu}
                    aria-label="Close mobile menu"
                >
                    <i className="fa-solid fa-xmark text-white text-xl" />
                </button>
                <div className="mobile-nav">
                    <ul>
                        <li>
                            <Link
                                href="/"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-home text-lg"></i>
                                Home
                            </Link>
                        </li>
                        <li>
                            <Link
                                href="/about"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/about" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-users text-lg"></i>
                                About Us
                            </Link>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/service"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/service") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-cogs text-lg"></i>
                                    Services
                                </Link>
                                <span className={`submenu-button${openSubMenus["service"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("service")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["service"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/service" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-list mr-2"></i>
                                        All Services
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/service/data-centre" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-server mr-2"></i>
                                        Data Centre Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/service/smart-city" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-city mr-2"></i>
                                        Smart City Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/service/it-consulting" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-laptop-code mr-2"></i>
                                        IT Consulting
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/service/software-development" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-code mr-2"></i>
                                        Software Development
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/service/training" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-graduation-cap mr-2"></i>
                                        Training & Support
                                    </Link>
                                </li>

                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/solutions"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/solutions") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-lightbulb text-lg"></i>
                                    Solutions
                                </Link>
                                <span className={`submenu-button${openSubMenus["solutions"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("solutions")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["solutions"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/solutions/digim" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-digital-tachograph mr-2"></i>
                                        DIGIM Platform
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/neteco" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-network-wired mr-2"></i>
                                        NetEco Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/fusion-module" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-cube mr-2"></i>
                                        FusionModule
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/ups" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-battery-full mr-2"></i>
                                        UPS Solutions
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/projects"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/projects") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-briefcase text-lg"></i>
                                    Projects
                                </Link>
                                <span className={`submenu-button${openSubMenus["projects"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("projects")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["projects"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/projects" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-folder-open mr-2"></i>
                                        All Projects
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/enhanced-projects" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-star mr-2"></i>
                                        Enhanced Projects
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/success-stories" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-trophy mr-2"></i>
                                        Success Stories
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/blog"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/blog") || pathname.startsWith("/case-studies") || pathname.startsWith("/whitepapers") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-book text-lg"></i>
                                    Resources
                                </Link>
                                <span className={`submenu-button${openSubMenus["resources"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("resources")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["resources"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/blog" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-newspaper mr-2"></i>
                                        Blog & News
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/case-studies" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-chart-line mr-2"></i>
                                        Case Studies
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/whitepapers" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-file-alt mr-2"></i>
                                        Whitepapers
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/technology-showcase" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-display mr-2"></i>
                                        Technology Showcase
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <Link
                                href="/contact"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/contact" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-envelope text-lg"></i>
                                Contact
                            </Link>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Pages
                                </Link>
                                <span className={`submenu-button${openSubMenus["pages"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("pages")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["pages"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/contact">Contact</Link>
                                </li>
                                <li>
                                    <Link href="/team">Team</Link>
                                </li>
                                <li>
                                    <Link href="/testimonial">Testimonial</Link>
                                </li>
                                <li>
                                    <Link href="/error">404</Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Blog
                                </Link>
                                <span className={`submenu-button${openSubMenus["blog"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("blog")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["blog"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/blog">Blog</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details-left">Details Left</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details-right">Details Right</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details">Blog Details</Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Project
                                </Link>
                                <span className={`submenu-button${openSubMenus["project"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("project")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["project"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/projects">Project</Link>
                                </li>
                                <li>
                                    <Link href="/projects-details-left">Project Left</Link>
                                </li>
                                <li>
                                    <Link href="/projects-details-right">Project Right</Link>
                                </li>
                                <li>
                                    <Link href="/projects-details">Project Details</Link>
                                </li>
                            </ul>
                        </li>
                    </ul>

                    <div className="mobile-button">
                        <Link className="theme-btn1" href="service">
                            Learn More
                            <span>
                                <i className="fa-solid fa-arrow-right" />
                            </span>
                        </Link>
                    </div>
                    <div className="single-footer-items">
                        <h3>Contact Us</h3>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon1.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="tel:+880123456789">+880 123 456 789</Link>
                            </div>
                        </div>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon2.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="mailto:<EMAIL>"><EMAIL></Link>
                            </div>
                        </div>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon3.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="tel:+880123456789">
                                    8502 Preston Rd. <br /> Inglewoo Maine 98380
                                </Link>
                            </div>
                        </div>
                    </div>
                    <div className="contact-infos">
                        <h3>Our Location</h3>
                        <ul className="social-icon">
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-linkedin-in" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-x-twitter" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-youtube" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-instagram" />
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </>
    );
}
