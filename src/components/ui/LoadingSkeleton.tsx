import React from 'react';

interface LoadingSkeletonProps {
  type: 'project' | 'stats' | 'category';
  count?: number;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ type, count = 1 }) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'project':
        return (
          <div className="col-lg-4 col-md-6">
            <div className="loading-skeleton project-card-skeleton"></div>
          </div>
        );
      
      case 'stats':
        return (
          <div className="col-lg-3 col-md-6">
            <div className="loading-skeleton stats-skeleton"></div>
          </div>
        );
      
      case 'category':
        return (
          <div className="col-lg-4 col-md-6">
            <div className="loading-skeleton category-skeleton"></div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <React.Fragment key={index}>
          {renderSkeleton()}
        </React.Fragment>
      ))}
    </>
  );
};

export default LoadingSkeleton;
