import React from 'react';
import { cn } from '@/lib/utils';

// Base block interface
export interface ContentBlock {
  id: string;
  type: string;
  data: any;
  order?: number;
}

// Hero Block
export interface HeroBlockData {
  title: string;
  subtitle?: string;
  description: string;
  backgroundImage?: string;
  ctaText?: string;
  ctaLink?: string;
}

export const HeroBlock: React.FC<{ data: HeroBlockData; className?: string }> = ({
  data,
  className
}) => (
  <section
    className={cn(
      "project-details-hero relative min-h-[80vh] flex items-center justify-center text-white overflow-hidden",
      className
    )}
    style={{
      backgroundImage: data.backgroundImage ? `url(${data.backgroundImage})` : undefined,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    }}
  >
    <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70" />
    <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/30" />

    {/* Animated background elements */}
    <div className="absolute inset-0 opacity-20">
      <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-purple-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-indigo-500/10 rounded-full blur-2xl animate-pulse delay-500"></div>
    </div>

    <div className="relative z-10 text-center max-w-6xl mx-auto px-4">
      {data.subtitle && (
        <div className="project-subtitle mb-6">
          <span className="inline-block px-6 py-2 bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-white/20 rounded-full text-blue-200 font-medium tracking-wide">
            {data.subtitle}
          </span>
        </div>
      )}
      <h1 className="project-title text-5xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight">
        <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
          {data.title}
        </span>
      </h1>
      <p className="project-description text-xl md:text-2xl mb-12 text-gray-200 leading-relaxed max-w-4xl mx-auto font-light">
        {data.description}
      </p>
      {data.ctaText && data.ctaLink && (
        <div className="project-cta">
          <a
            href={data.ctaLink}
            className="inline-flex items-center px-10 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
          >
            {data.ctaText}
            <svg className="ml-3 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </div>
      )}
    </div>

    {/* Scroll indicator */}
    <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
      <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
        <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
      </div>
    </div>
  </section>
);

// Text Block
export interface TextBlockData {
  content: string; // TipTap HTML content
  alignment?: 'left' | 'center' | 'right';
}

export const TextBlock: React.FC<{ data: TextBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <div 
    className={cn(
      "prose prose-lg max-w-none",
      data.alignment === 'center' && "text-center",
      data.alignment === 'right' && "text-right",
      className
    )}
    dangerouslySetInnerHTML={{ __html: data.content }}
  />
);

// Image Block
export interface ImageBlockData {
  src: string;
  alt: string;
  caption?: string;
  width?: number;
  height?: number;
  alignment?: 'left' | 'center' | 'right';
}

export const ImageBlock: React.FC<{ data: ImageBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <figure 
    className={cn(
      "my-8",
      data.alignment === 'center' && "text-center",
      data.alignment === 'right' && "text-right",
      className
    )}
  >
    <img 
      src={data.src} 
      alt={data.alt}
      width={data.width}
      height={data.height}
      className="max-w-full h-auto rounded-lg shadow-lg"
    />
    {data.caption && (
      <figcaption className="mt-2 text-sm text-gray-600 italic">
        {data.caption}
      </figcaption>
    )}
  </figure>
);

// Gallery Block
export interface GalleryBlockData {
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  columns?: 2 | 3 | 4;
}

export const GalleryBlock: React.FC<{ data: GalleryBlockData; className?: string }> = ({
  data,
  className
}) => (
  <section className={cn("project-gallery-section sp", className)}>
    <div className="container">
      <div className="row">
        <div className="col-lg-6 m-auto text-center">
          <div className="heading1">
            <span className="span">Visual Showcase</span>
            <h2>Project Gallery</h2>
            <div className="space16" />
            <p>Explore the visual journey and key moments of this transformative project</p>
          </div>
        </div>
      </div>
      <div className="space60" />
      <div
        className={cn(
          "row",
          data.columns === 2 && "justify-content-center"
        )}
      >
        {data.images.map((image, index) => (
          <div key={index} className={cn(
            data.columns === 2 ? "col-lg-6 col-md-6" :
            data.columns === 4 ? "col-lg-3 col-md-6" :
            "col-lg-4 col-md-6"
          )}>
            <div className="gallery-item">
              <div className="image">
                <img src={image.src} alt={image.alt} />
                <div className="overlay">
                  <div className="overlay-content">
                    <h4>{image.caption || image.alt}</h4>
                  </div>
                </div>
              </div>
            </div>
            <div className="space30" />
          </div>
        ))}
      </div>
    </div>
  </section>
);

// Stats Block
export interface StatsBlockData {
  title?: string;
  stats: Array<{
    number: string;
    label: string;
    description?: string;
  }>;
  layout?: 'horizontal' | 'grid';
}

export const StatsBlock: React.FC<{ data: StatsBlockData; className?: string }> = ({
  data,
  className
}) => (
  <section className={cn("project-stats-section sp", className)}>
    <div className="container">
      {data.title && (
        <div className="row">
          <div className="col-lg-6 m-auto text-center">
            <div className="heading1">
              <span className="span">Performance Metrics</span>
              <h2>{data.title}</h2>
              <div className="space16" />
              <p>Exceptional results delivered through innovative technology and strategic implementation</p>
            </div>
          </div>
        </div>
      )}
      <div className="space60" />
      <div
        className={cn(
          "row",
          data.layout === 'horizontal' ? "justify-content-center" : ""
        )}
      >
        {data.stats.map((stat, index) => (
          <div key={index} className={cn(
            data.layout === 'horizontal' ? "col-lg-3 col-md-6" : "col-lg-4 col-md-6"
          )}>
            <div className="stats-item">
              <div className="stats-number">
                <span className="counter">{stat.number}</span>
              </div>
              <h4>{stat.label}</h4>
              {stat.description && (
                <p className="stats-description">{stat.description}</p>
              )}
            </div>
            <div className="space30" />
          </div>
        ))}
      </div>
    </div>
  </section>
);

// Features Block
export interface FeaturesBlockData {
  title?: string;
  features: Array<{
    icon?: string;
    title: string;
    description: string;
  }>;
  layout?: 'grid' | 'list';
}

export const FeaturesBlock: React.FC<{ data: FeaturesBlockData; className?: string }> = ({
  data,
  className
}) => (
  <section className={cn("project-features-section sp", className)}>
    <div className="container">
      {data.title && (
        <div className="row">
          <div className="col-lg-6 m-auto text-center">
            <div className="heading1">
              <span className="span">Key Components</span>
              <h2>{data.title}</h2>
              <div className="space16" />
              <p>Discover the innovative features and capabilities that make this project exceptional</p>
            </div>
          </div>
        </div>
      )}
      <div className="space60" />
      <div className="row">
        {data.features.map((feature, index) => (
          <div key={index} className="col-lg-4 col-md-6">
            <div className="feature-item">
              {feature.icon && (
                <div className="feature-icon">
                  <i className={feature.icon}></i>
                </div>
              )}
              <div className="feature-content">
                <h3>{feature.title}</h3>
                <div className="space16" />
                <p>{feature.description}</p>
              </div>
            </div>
            <div className="space30" />
          </div>
        ))}
      </div>
    </div>
  </section>
);

// Timeline Block
export interface TimelineBlockData {
  title?: string;
  phases: Array<{
    phase: string;
    duration: string;
    title: string;
    description: string;
    status?: 'completed' | 'current' | 'upcoming';
  }>;
}

export const TimelineBlock: React.FC<{ data: TimelineBlockData; className?: string }> = ({ 
  data, 
  className 
}) => (
  <section className={cn("py-12", className)}>
    {data.title && (
      <h2 className="text-3xl font-bold text-center mb-12">{data.title}</h2>
    )}
    <div className="max-w-4xl mx-auto">
      {data.phases.map((phase, index) => (
        <div key={index} className="flex items-start mb-8 last:mb-0">
          <div className="flex-shrink-0 w-24 text-right mr-8">
            <div className={cn(
              "inline-block px-3 py-1 rounded-full text-sm font-semibold",
              phase.status === 'completed' && "bg-green-100 text-green-800",
              phase.status === 'current' && "bg-blue-100 text-blue-800",
              phase.status === 'upcoming' && "bg-gray-100 text-gray-800",
              !phase.status && "bg-gray-100 text-gray-800"
            )}>
              {phase.phase}
            </div>
            <div className="text-sm text-gray-500 mt-1">{phase.duration}</div>
          </div>
          <div className="flex-shrink-0 w-4 h-4 bg-blue-600 rounded-full mt-2 mr-8"></div>
          <div className="flex-grow">
            <h3 className="text-xl font-semibold mb-2">{phase.title}</h3>
            <p className="text-gray-600">{phase.description}</p>
          </div>
        </div>
      ))}
    </div>
  </section>
);

// CTA Block
export interface CTABlockData {
  title: string;
  description: string;
  primaryButton?: {
    text: string;
    link: string;
  };
  secondaryButton?: {
    text: string;
    link: string;
  };
  backgroundColor?: string;
}

export const CTABlock: React.FC<{ data: CTABlockData; className?: string }> = ({
  data,
  className
}) => (
  <section
    className={cn("cta-section sp", className)}
    style={{
      background: data.backgroundColor ?
        `linear-gradient(135deg, ${data.backgroundColor} 0%, ${data.backgroundColor}dd 100%)` :
        'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)'
    }}
  >
    <div className="container">
      <div className="row">
        <div className="col-lg-8 m-auto text-center">
          <div className="heading1">
            <h2 className="text-white">{data.title}</h2>
            <div className="space16" />
            <p className="text-white opacity-90">{data.description}</p>
            <div className="space30" />
            <div className="cta-buttons">
              {data.primaryButton && (
                <a
                  href={data.primaryButton.link}
                  className="theme-btn1"
                >
                  {data.primaryButton.text}
                  <span><i className="bi bi-arrow-right"></i></span>
                </a>
              )}
              {data.secondaryButton && (
                <a
                  href={data.secondaryButton.link}
                  className="theme-btn2"
                >
                  {data.secondaryButton.text}
                  <span><i className="bi bi-arrow-right"></i></span>
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

// Block Renderer
export const BlockRenderer: React.FC<{ 
  blocks: ContentBlock[]; 
  className?: string 
}> = ({ blocks, className }) => {
  const sortedBlocks = blocks.sort((a, b) => (a.order || 0) - (b.order || 0));

  return (
    <div className={className}>
      {sortedBlocks.map((block) => {
        switch (block.type) {
          case 'hero':
            return <HeroBlock key={block.id} data={block.data} />;
          case 'text':
            return <TextBlock key={block.id} data={block.data} />;
          case 'image':
            return <ImageBlock key={block.id} data={block.data} />;
          case 'gallery':
            return <GalleryBlock key={block.id} data={block.data} />;
          case 'stats':
            return <StatsBlock key={block.id} data={block.data} />;
          case 'features':
            return <FeaturesBlock key={block.id} data={block.data} />;
          case 'timeline':
            return <TimelineBlock key={block.id} data={block.data} />;
          case 'cta':
            return <CTABlock key={block.id} data={block.data} />;
          default:
            return null;
        }
      })}
    </div>
  );
};