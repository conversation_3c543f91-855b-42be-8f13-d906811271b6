"use client";
import React from 'react';
import Link from 'next/link';
import { getServiceImage, getServiceIcon } from '@/utils/serviceImages';

interface Service {
  id: string;
  pageBanner: {
    pageTitle: string;
    description: string;
  };
  about?: {
    image?: {
      src: string;
      alt?: string;
    };
  };
}

interface ServiceRelatedProps {
  services: Service[];
}

const ServiceRelated: React.FC<ServiceRelatedProps> = ({ services }) => {
  if (!services || services.length === 0) {
    return null;
  }

  return (
    <section className="service-related-section sp">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span">Explore More</span>
              <h2>Related Services</h2>
              <div className="space16" />
              <p>Discover other services that complement your technology needs and help you achieve comprehensive digital transformation.</p>
            </div>
          </div>
        </div>
        <div className="space60" />

        <div className="related-services-grid">
          {services.map((service) => (
            <div key={service.id} className="related-service-card">
              <Link href={`/service/${service.id}`} className="service-link">
                <div className="service-image">
                  <img
                    src={service.about?.image?.src || getServiceImage(service.id)}
                    alt={service.about?.image?.alt || service.pageBanner.pageTitle}
                    className="service-img"
                  />
                  <div className="service-overlay">
                    <div className="service-icon">
                      <span className="icon-emoji">{getServiceIcon(service.id)}</span>
                    </div>
                  </div>
                </div>
                <div className="service-content">
                  <h3 className="service-title">{service.pageBanner.pageTitle}</h3>
                  <p className="service-description">{service.pageBanner.description}</p>
                  <div className="service-cta">
                    <span className="cta-text">Learn More</span>
                    <i className="fas fa-arrow-right cta-icon"></i>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        <div className="space60" />
        <div className="text-center">
          <Link href="/service" className="theme-btn1">
            View All Services
            <span>
              <i className="fas fa-arrow-right"></i>
            </span>
          </Link>
        </div>
      </div>

      <style jsx>{`
        .service-related-section {
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .related-services-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 2rem;
        }

        .related-service-card {
          background: white;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
          border: 1px solid rgba(3, 39, 110, 0.1);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
        }

        .related-service-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #03276e, #e89d1a, #03276e);
          transform: scaleX(0);
          transition: transform 0.3s ease;
        }

        .related-service-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .related-service-card:hover::before {
          transform: scaleX(1);
        }

        .service-link {
          display: block;
          text-decoration: none;
          color: inherit;
        }

        .service-link:hover {
          text-decoration: none;
          color: inherit;
        }

        .service-image {
          position: relative;
          height: 200px;
          overflow: hidden;
        }

        .service-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.4s ease;
        }

        .related-service-card:hover .service-img {
          transform: scale(1.1);
        }

        .service-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(3, 39, 110, 0.8) 0%, rgba(30, 64, 175, 0.6) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .related-service-card:hover .service-overlay {
          opacity: 1;
        }

        .service-icon {
          transform: translateY(20px);
          transition: transform 0.3s ease;
        }

        .related-service-card:hover .service-icon {
          transform: translateY(0);
        }

        .icon-emoji {
          font-size: 3rem;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .service-content {
          padding: 2rem;
        }

        .service-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 1rem;
          line-height: 1.3;
        }

        .service-description {
          font-size: 1rem;
          color: #64748b;
          line-height: 1.6;
          margin-bottom: 1.5rem;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .service-cta {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #03276e;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        .related-service-card:hover .service-cta {
          color: #1e40af;
        }

        .cta-icon {
          transition: transform 0.3s ease;
        }

        .related-service-card:hover .cta-icon {
          transform: translateX(5px);
        }

        .theme-btn1 {
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem 2rem;
          background: linear-gradient(45deg, #03276e 0%, #1e40af 100%);
          color: white;
          text-decoration: none;
          border-radius: 50px;
          font-weight: 600;
          font-size: 1.1rem;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 10px 30px rgba(3, 39, 110, 0.3);
        }

        .theme-btn1:hover {
          transform: translateY(-3px);
          box-shadow: 0 15px 35px rgba(3, 39, 110, 0.4);
          color: white;
          text-decoration: none;
        }

        .theme-btn1 i {
          transition: transform 0.3s ease;
        }

        .theme-btn1:hover i {
          transform: translateX(3px);
        }

        @media (max-width: 768px) {
          .related-services-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
          }

          .service-content {
            padding: 1.5rem;
          }

          .service-title {
            font-size: 1.3rem;
          }
        }

        @media (max-width: 576px) {
          .related-services-grid {
            grid-template-columns: 1fr;
          }

          .service-image {
            height: 180px;
          }

          .icon-emoji {
            font-size: 2.5rem;
          }
        }
      `}</style>
    </section>
  );
};

export default ServiceRelated;
