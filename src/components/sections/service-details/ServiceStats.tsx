"use client";
import React from 'react';

interface ServiceStatsProps {
  serviceId: string;
}

const ServiceStats: React.FC<ServiceStatsProps> = ({ serviceId }) => {
  // Define stats based on service type
  const getServiceStats = (id: string) => {
    const statsMap: Record<string, Array<{
      number: string;
      label: string;
      description: string;
    }>> = {
      'smart-city': [
        { number: '500+', label: 'Cameras Deployed', description: 'Advanced surveillance systems' },
        { number: '24/7', label: 'Monitoring', description: 'Continuous security coverage' },
        { number: '95%', label: 'Crime Reduction', description: 'Proven safety improvement' },
        { number: '10+', label: 'Cities Protected', description: 'Across multiple regions' }
      ],
      'data-center': [
        { number: '99.9%', label: 'Uptime Guarantee', description: 'Enterprise-grade reliability' },
        { number: '50+', label: 'Data Centers', description: 'Built and maintained' },
        { number: '24/7', label: 'Support', description: 'Round-the-clock assistance' },
        { number: '100TB+', label: 'Storage Capacity', description: 'Scalable infrastructure' }
      ],
      'software-development': [
        { number: '200+', label: 'Applications Built', description: 'Custom software solutions' },
        { number: '15+', label: 'Programming Languages', description: 'Technology expertise' },
        { number: '98%', label: 'Client Satisfaction', description: 'Quality delivery rate' },
        { number: '6 Months', label: 'Average Delivery', description: 'Efficient development cycle' }
      ],
      'it-consulting': [
        { number: '17+', label: 'Years Experience', description: 'Industry expertise' },
        { number: '300+', label: 'Clients Served', description: 'Successful partnerships' },
        { number: '50+', label: 'IT Specialists', description: 'Expert team members' },
        { number: '24/7', label: 'Support Available', description: 'Always here to help' }
      ],
      'live-streaming': [
        { number: '1M+', label: 'Viewers Reached', description: 'Broadcast capacity' },
        { number: '4K', label: 'Ultra HD Quality', description: 'Premium streaming' },
        { number: '99.9%', label: 'Stream Reliability', description: 'Uninterrupted service' },
        { number: '50+', label: 'Events Streamed', description: 'Successful broadcasts' }
      ],
      'video-production': [
        { number: '500+', label: 'Videos Produced', description: 'Professional content' },
        { number: '4K', label: 'Ultra HD Quality', description: 'Premium production' },
        { number: '48hrs', label: 'Quick Turnaround', description: 'Fast delivery' },
        { number: '100%', label: 'Client Satisfaction', description: 'Quality guarantee' }
      ],
      'training': [
        { number: '1000+', label: 'Students Trained', description: 'Knowledge transfer' },
        { number: '20+', label: 'Course Modules', description: 'Comprehensive curriculum' },
        { number: '95%', label: 'Certification Rate', description: 'Success achievement' },
        { number: '24/7', label: 'Learning Support', description: 'Continuous assistance' }
      ],
      'graphic-design': [
        { number: '800+', label: 'Designs Created', description: 'Creative solutions' },
        { number: '48hrs', label: 'Quick Delivery', description: 'Fast turnaround' },
        { number: '100%', label: 'Original Content', description: 'Unique designs' },
        { number: '50+', label: 'Brand Identities', description: 'Complete branding' }
      ],
      'boardroom-solutions': [
        { number: '100+', label: 'Boardrooms Equipped', description: 'Professional setups' },
        { number: '4K', label: 'Display Quality', description: 'Crystal clear visuals' },
        { number: '24/7', label: 'Technical Support', description: 'Always available' },
        { number: '99%', label: 'System Reliability', description: 'Dependable technology' }
      ]
    };

    return statsMap[id] || [
      { number: '17+', label: 'Years Experience', description: 'Industry expertise' },
      { number: '500+', label: 'Projects Completed', description: 'Successful deliveries' },
      { number: '24/7', label: 'Support Available', description: 'Always here to help' },
      { number: '100%', label: 'Client Satisfaction', description: 'Quality guarantee' }
    ];
  };

  const stats = getServiceStats(serviceId);

  return (
    <section className="service-stats-section">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span">Performance Metrics</span>
              <h2>Proven Results & Excellence</h2>
              <div className="space16" />
              <p>Our track record speaks for itself. Here are the key metrics that demonstrate our commitment to delivering exceptional results.</p>
            </div>
          </div>
        </div>
        
        <div className="service-stats-grid">
          {stats.map((stat, index) => (
            <div key={index} className="service-stat-item">
              <div className="service-stat-number">{stat.number}</div>
              <div className="service-stat-label">{stat.label}</div>
              <div className="service-stat-description">{stat.description}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServiceStats;
