"use client";
import React, { useState } from 'react';

type Feature = {
  num: string;
  title: string;
  text: string;
};

type FeaturesProps = {
  features: {
    title: string;
    description: string;
    features: Feature[];
  };
};

const ServiceFeatures: React.FC<FeaturesProps> = ({ features }) => {
  const [open, setOpen] = useState<number | null>(0);
  return (
    <section className="service-features-enhanced">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span">Service Features</span>
              <h2>{features.title}</h2>
              <div className="space16" />
              <p>{features.description}</p>
            </div>
          </div>
        </div>
        <div className="space60" />

        <div className="service-features-grid">
          {features.features.map((f, idx) => (
            <div key={f.num} className="service-feature-card">
              <div className="service-feature-number">{f.num}</div>
              <h3 className="service-feature-title">{f.title}</h3>
              <p className="service-feature-text">{f.text}</p>
            </div>
          ))}
        </div>
      </div>
      <style jsx>{`
        /* Styles are now handled by the main CSS file */
      `}</style>
    </section>
  );
};

export default ServiceFeatures; 