"use client";
import React from 'react';
import Link from 'next/link';

type BannerProps = {
  banner: {
    pageTitle: string;
    breadTitle: string;
    description: string;
    type?: string;
  };
  serviceId?: string;
};

const ServiceBanner: React.FC<BannerProps> = ({ banner, serviceId }) => (
  <section className="service-banner-enhanced text-center position-relative">
    <div className="container position-relative z-2">
      <div className="banner-content">
        <span className="badge bg-primary text-white px-4 py-2 rounded-pill mb-4 fade-in-up">
          Motshwanelo IT Consulting
        </span>
        <h1 className="service-banner-title fade-in-up">{banner.pageTitle}</h1>
        <p className="service-banner-description fade-in-up" style={{animationDelay:'0.15s'}}>{banner.description}</p>

        <div className="breadcrumb mb-4 d-flex justify-content-center align-items-center gap-2 fade-in-up" style={{animationDelay:'0.3s'}}>
          <Link href="/" className="breadcrumb-link">
            <span className="breadcrumb-icon">🏠</span>
            <span>Home</span>
          </Link>
          <span className="mx-2 text-white-50">/</span>
          <Link href="/service" className="breadcrumb-link">
            <span className="breadcrumb-icon">🛠️</span>
            <span>Services</span>
          </Link>
          <span className="mx-2 text-white-50">/</span>
          <span className="fw-bold text-white">{banner.breadTitle}</span>
        </div>

        <div className="banner-cta fade-in-up" style={{animationDelay:'0.45s'}}>
          <Link href="#contact" className="service-banner-cta">
            <span>Get Started Today</span>
            <i className="fas fa-arrow-right"></i>
          </Link>
        </div>
        <div className="service-highlights fade-in-up" style={{animationDelay:'0.45s'}}>
          <div className="row justify-content-center">
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-check-circle text-primary"></i>
                <span>17+ Years Experience</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-award text-primary"></i>
                <span>Enterprise Grade</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-clock text-primary"></i>
                <span>24/7 Support</span>
              </div>
            </div>
            <div className="col-md-3 col-6 mb-2">
              <div className="highlight-item">
                <i className="fas fa-users text-primary"></i>
                <span>500+ Projects</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <style jsx>{`
      .fade-in-up {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.8s forwards;
      }
      .fade-in-up[style*='0.15s'] { animation-delay: 0.15s; }
      .fade-in-up[style*='0.3s'] { animation-delay: 0.3s; }
      .fade-in-up[style*='0.45s'] { animation-delay: 0.45s; }

      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: none;
        }
      }

      .breadcrumb-icon {
        font-size: 1.1em;
        margin-right: 0.5rem;
      }

      .breadcrumb-link {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: color 0.3s ease;
        display: flex;
        align-items: center;
      }

      .breadcrumb-link:hover {
        color: white;
        text-decoration: none;
      }

      .badge {
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
      }

      .banner-content {
        width: 100%;
        max-width: 900px;
        margin: 0 auto;
      }

      @media (max-width: 768px) {
        .breadcrumb {
          flex-direction: column;
          gap: 0.5rem;
        }

        .breadcrumb-link {
          font-size: 0.9rem;
        }
      }
    `}</style>
  </section>
);

export default ServiceBanner; 