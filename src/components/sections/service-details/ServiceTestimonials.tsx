"use client";
import React, { useState } from 'react';

interface ServiceTestimonialsProps {
  serviceId: string;
}

const ServiceTestimonials: React.FC<ServiceTestimonialsProps> = ({ serviceId }) => {
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  // Define testimonials based on service type
  const getServiceTestimonials = (id: string) => {
    const testimonialsMap: Record<string, Array<{
      name: string;
      role: string;
      company: string;
      content: string;
      rating: number;
      avatar?: string;
    }>> = {
      'smart-city': [
        {
          name: 'Dr. <PERSON>',
          role: 'City Manager',
          company: 'Johannesburg Metropolitan Municipality',
          content: 'The smart city solution has transformed our urban safety. Crime rates have dropped by 40% in monitored areas, and our response times have improved dramatically.',
          rating: 5
        },
        {
          name: '<PERSON>',
          role: 'Security Director',
          company: 'Cape Town Safety Initiative',
          content: 'Outstanding implementation of surveillance systems. The real-time monitoring and alert system has been a game-changer for our public safety operations.',
          rating: 5
        }
      ],
      'data-center': [
        {
          name: '<PERSON>',
          role: 'IT Director',
          company: 'Standard Bank',
          content: 'MITC delivered a world-class data center that exceeds our uptime requirements. Their expertise in enterprise infrastructure is unmatched.',
          rating: 5
        },
        {
          name: '<PERSON>',
          role: 'CTO',
          company: 'Discovery Health',
          content: 'The data center infrastructure they built for us has been rock-solid. Zero downtime in 18 months of operation. Exceptional work!',
          rating: 5
        }
      ],
      'software-development': [
        {
          name: 'Lisa Anderson',
          role: 'Product Manager',
          company: 'FNB Innovation',
          content: 'The custom software solution delivered by MITC has streamlined our operations significantly. Their development team is top-notch.',
          rating: 5
        },
        {
          name: 'Robert Nkomo',
          role: 'CEO',
          company: 'TechStart Africa',
          content: 'From concept to deployment, MITC handled our software development project with professionalism and expertise. Highly recommended!',
          rating: 5
        }
      ],
      'it-consulting': [
        {
          name: 'Amanda Foster',
          role: 'Operations Director',
          company: 'Woolworths Holdings',
          content: 'MITC\'s IT consulting services helped us modernize our entire technology stack. Their strategic approach saved us both time and money.',
          rating: 5
        },
        {
          name: 'Thabo Molefe',
          role: 'IT Manager',
          company: 'Sasol Limited',
          content: 'Excellent consulting services. They understood our complex requirements and delivered solutions that perfectly fit our business needs.',
          rating: 5
        }
      ]
    };

    return testimonialsMap[id] || [
      {
        name: 'John Smith',
        role: 'Technology Director',
        company: 'Enterprise Solutions Ltd',
        content: 'MITC has been our trusted technology partner for over 5 years. Their expertise and dedication to excellence is unmatched in the industry.',
        rating: 5
      },
      {
        name: 'Mary Johnson',
        role: 'CEO',
        company: 'Innovation Corp',
        content: 'Working with MITC has transformed our business operations. Their solutions are innovative, reliable, and perfectly tailored to our needs.',
        rating: 5
      }
    ];
  };

  const testimonials = getServiceTestimonials(serviceId);

  const nextTestimonial = () => {
    setActiveTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setActiveTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`star ${i < rating ? 'filled' : ''}`}>★</span>
    ));
  };

  return (
    <section className="service-testimonials-section sp">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span">Client Success Stories</span>
              <h2>What Our Clients Say</h2>
              <div className="space16" />
              <p>Don't just take our word for it. Here's what our satisfied clients have to say about our services.</p>
            </div>
          </div>
        </div>
        <div className="space60" />

        <div className="testimonials-carousel">
          <div className="testimonial-card">
            <div className="testimonial-content">
              <div className="quote-icon">
                <i className="fas fa-quote-left"></i>
              </div>
              <div className="rating">
                {renderStars(testimonials[activeTestimonial].rating)}
              </div>
              <p className="testimonial-text">
                "{testimonials[activeTestimonial].content}"
              </p>
              <div className="testimonial-author">
                <div className="author-avatar">
                  <div className="avatar-placeholder">
                    {testimonials[activeTestimonial].name.split(' ').map(n => n[0]).join('')}
                  </div>
                </div>
                <div className="author-info">
                  <h4 className="author-name">{testimonials[activeTestimonial].name}</h4>
                  <p className="author-role">{testimonials[activeTestimonial].role}</p>
                  <p className="author-company">{testimonials[activeTestimonial].company}</p>
                </div>
              </div>
            </div>
          </div>

          {testimonials.length > 1 && (
            <div className="testimonial-controls">
              <button className="testimonial-btn prev" onClick={prevTestimonial}>
                <i className="fas fa-chevron-left"></i>
              </button>
              <div className="testimonial-dots">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    className={`dot ${index === activeTestimonial ? 'active' : ''}`}
                    onClick={() => setActiveTestimonial(index)}
                  />
                ))}
              </div>
              <button className="testimonial-btn next" onClick={nextTestimonial}>
                <i className="fas fa-chevron-right"></i>
              </button>
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        .service-testimonials-section {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .testimonials-carousel {
          max-width: 800px;
          margin: 0 auto;
        }

        .testimonial-card {
          background: white;
          padding: 3rem;
          border-radius: 20px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(3, 39, 110, 0.1);
          position: relative;
          overflow: hidden;
        }

        .testimonial-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #03276e, #e89d1a, #03276e);
        }

        .quote-icon {
          font-size: 3rem;
          color: #03276e;
          opacity: 0.2;
          margin-bottom: 1rem;
        }

        .rating {
          margin-bottom: 1.5rem;
        }

        .star {
          font-size: 1.5rem;
          color: #e2e8f0;
          margin-right: 0.25rem;
        }

        .star.filled {
          color: #fbbf24;
        }

        .testimonial-text {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #475569;
          margin-bottom: 2rem;
          font-style: italic;
        }

        .testimonial-author {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .author-avatar {
          flex-shrink: 0;
        }

        .avatar-placeholder {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #03276e 0%, #1e40af 100%);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1.2rem;
        }

        .author-info {
          flex-grow: 1;
        }

        .author-name {
          font-size: 1.2rem;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 0.25rem;
        }

        .author-role {
          font-size: 1rem;
          color: #64748b;
          margin-bottom: 0.25rem;
        }

        .author-company {
          font-size: 0.9rem;
          color: #03276e;
          font-weight: 600;
          margin: 0;
        }

        .testimonial-controls {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 2rem;
          margin-top: 2rem;
        }

        .testimonial-btn {
          width: 50px;
          height: 50px;
          border: none;
          background: linear-gradient(135deg, #03276e 0%, #1e40af 100%);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(3, 39, 110, 0.3);
        }

        .testimonial-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(3, 39, 110, 0.4);
        }

        .testimonial-dots {
          display: flex;
          gap: 0.5rem;
        }

        .dot {
          width: 12px;
          height: 12px;
          border: none;
          background: #cbd5e1;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .dot.active {
          background: #03276e;
          transform: scale(1.2);
        }

        @media (max-width: 768px) {
          .testimonial-card {
            padding: 2rem;
          }

          .testimonial-text {
            font-size: 1.1rem;
          }

          .testimonial-author {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
          }
        }
      `}</style>
    </section>
  );
};

export default ServiceTestimonials;
