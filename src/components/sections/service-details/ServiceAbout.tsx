"use client";
import React from 'react';

type AboutProps = {
  about: {
    title: string;
    description: string;
    image?: { src: string; alt?: string };
  };
};

const ServiceAbout: React.FC<AboutProps> = ({ about }) => (
  <section className="service-about-enhanced">
    <div className="container">
      <div className="row align-items-center">
        {about.image && (
          <div className="col-lg-6 mb-4 mb-lg-0 slide-in-left">
            <div className="service-about-image">
              <img src={about.image.src} alt={about.image.alt || about.title} />
            </div>
          </div>
        )}
        <div className={`${about.image ? 'col-lg-6' : 'col-lg-10 m-auto'} fade-in`}>
          <div className="service-about-content">
            <h2 className="service-about-title">{about.title}</h2>
            <div className="service-about-text" style={{ whiteSpace: 'pre-line' }}>
              {about.description}
            </div>
            <div className="about-features">
              <div className="feature-item">
                <i className="fas fa-check-circle"></i>
                <span>Enterprise-grade solutions</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-check-circle"></i>
                <span>24/7 technical support</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-check-circle"></i>
                <span>Scalable architecture</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <style jsx>{`
      .slide-in-left {
        opacity: 0;
        transform: translateX(-50px);
        animation: slideInLeft 0.8s 0.2s forwards;
      }

      @keyframes slideInLeft {
        to {
          opacity: 1;
          transform: none;
        }
      }

      .fade-in {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.8s 0.4s forwards;
      }

      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: none;
        }
      }

      .about-features {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1rem;
        font-weight: 500;
        color: #475569;
      }

      .feature-item i {
        color: #10b981;
        font-size: 1.2rem;
      }

      @media (max-width: 768px) {
        .about-features {
          margin-top: 1.5rem;
        }

        .feature-item {
          font-size: 0.9rem;
        }
      }
    `}</style>
  </section>
);

export default ServiceAbout; 