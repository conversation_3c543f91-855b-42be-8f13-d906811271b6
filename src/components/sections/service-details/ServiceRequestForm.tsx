"use client";
import React, { useState } from 'react';

type FormField = {
  type: string;
  name: string;
  label: string;
  placeholder?: string;
};

type RequestFormProps = {
  form: {
    title: string;
    description: string;
    'form-fields': FormField[];
  };
};

const ServiceRequestForm: React.FC<RequestFormProps> = ({ form }) => {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    setTimeout(() => setSubmitted(false), 2000);
  };

  return (
    <section className="service-request-form sp bg-white position-relative" id="contact">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 m-auto text-center">
            <div className="heading1">
              <span className="span">Get In Touch</span>
              <h2>{form.title}</h2>
              <div className="space16" />
              <p>{form.description}</p>
            </div>
          </div>
        </div>
        <div className="space60" />

        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="contact-form-wrapper">
              <form onSubmit={handleSubmit}>
          {form['form-fields'].map((field) => {
            if (field.type === 'submit') {
              return (
                <div className="text-center" key={field.name}>
                  <button type="submit" className="theme-btn1 animated-submit-btn">
                    {field.label}
                    <span>
                      <i className="fas fa-paper-plane"></i>
                    </span>
                  </button>
                </div>
              );
            }
            if (field.type === 'textarea') {
              return (
                <div className="form-group" key={field.name}>
                  <label className="form-label">{field.label}</label>
                  <textarea
                    className="form-control animated-field"
                    name={field.name}
                    placeholder={field.placeholder || `Enter your ${field.label.toLowerCase()}`}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                    rows={5}
                  />
                </div>
              );
            }
            return (
              <div className="form-group" key={field.name}>
                <label className="form-label">{field.label}</label>
                <input
                  className="form-control animated-field"
                  type={field.type}
                  name={field.name}
                  placeholder={field.placeholder || `Enter your ${field.label.toLowerCase()}`}
                  value={formData[field.name] || ''}
                  onChange={handleChange}
                />
              </div>
            );
          })}
              </form>
            </div>
          </div>
        </div>
        {submitted && (
          <div className="form-success-animation text-center mt-4">
            <span className="checkmark">✔️</span>
            <div className="confetti">🎉🎉🎉</div>
            <div className="fw-bold text-success mt-2">Thank you! We'll be in touch soon.</div>
          </div>
        )}
      </div>
      <style jsx>{`
        .contact-form-wrapper {
          background: white;
          padding: 3rem;
          border-radius: 20px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(3, 39, 110, 0.1);
          position: relative;
          overflow: hidden;
        }

        .contact-form-wrapper::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #03276e, #e89d1a, #03276e);
        }

        .form-group {
          margin-bottom: 2rem;
        }

        .form-label {
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 0.75rem;
          font-size: 1rem;
        }

        .animated-field {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border: 2px solid #e2e8f0;
          border-radius: 12px;
          padding: 1rem 1.25rem;
          font-size: 1rem;
          background: #f8fafc;
          width: 100%;
        }

        .animated-field:focus {
          border-color: #03276e;
          box-shadow: 0 0 0 3px rgba(3, 39, 110, 0.1);
          transform: translateY(-2px);
          background: white;
          outline: none;
        }

        .animated-submit-btn {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          margin-top: 1rem;
        }

        .animated-submit-btn:hover {
          transform: translateY(-3px);
          box-shadow: 0 15px 35px rgba(3, 39, 110, 0.4);
        }

        .theme-btn1 {
          display: inline-flex;
          align-items: center;
          gap: 0.75rem;
          padding: 1rem 2.5rem;
          background: linear-gradient(45deg, #03276e 0%, #1e40af 100%);
          color: white;
          text-decoration: none;
          border: none;
          border-radius: 50px;
          font-weight: 600;
          font-size: 1.1rem;
          cursor: pointer;
          box-shadow: 0 10px 30px rgba(3, 39, 110, 0.3);
        }

        .theme-btn1 i {
          transition: transform 0.3s ease;
        }

        .theme-btn1:hover i {
          transform: translateX(3px);
        }

        .form-success-animation {
          animation: popIn 0.5s;
          background: white;
          padding: 2rem;
          border-radius: 15px;
          box-shadow: 0 10px 30px rgba(16, 185, 129, 0.2);
          border: 2px solid #10b981;
        }

        @keyframes popIn {
          0% { opacity: 0; transform: scale(0.7); }
          80% { opacity: 1; transform: scale(1.05); }
          100% { opacity: 1; transform: scale(1); }
        }

        .checkmark {
          font-size: 3rem;
          color: #10b981;
          display: block;
          margin-bottom: 1rem;
        }

        .confetti {
          font-size: 1.5rem;
          margin: 0.5rem 0;
          animation: bounce 1s infinite;
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          60% { transform: translateY(-5px); }
        }

        @media (max-width: 768px) {
          .contact-form-wrapper {
            padding: 2rem;
          }

          .animated-field {
            padding: 0.875rem 1rem;
          }

          .theme-btn1 {
            padding: 0.875rem 2rem;
            font-size: 1rem;
          }
        }
      `}</style>
    </section>
  );
};

export default ServiceRequestForm; 